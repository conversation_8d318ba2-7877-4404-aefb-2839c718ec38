# Supabase Keep-Alive Service

A simple automation project to keep your Supabase database active on the free tier by pinging it periodically. This prevents the project from pausing after 7 days of inactivity.

## 🚀 Features

- **Automated Daily Pings**: Runs every day at midnight UTC using Vercel's cron jobs
- **Minimal Resource Usage**: Lightweight serverless function that stays within free tiers
- **Comprehensive Logging**: Detailed logs for monitoring and debugging
- **Error Handling**: Robust error handling with different response codes
- **Security**: Uses environment variables to keep credentials secure
- **Easy Testing**: Local test script to verify configuration before deployment

## 📋 Prerequisites

Before you begin, ensure you have:

1. **Node.js** (version 18 or higher) installed on your machine
2. **A Supabase project** with a `keep_alive` table
3. **A Vercel account** (free tier is sufficient)
4. **Vercel CLI** installed globally: `npm install -g vercel`

## 🗄️ Supabase Setup

### 1. Create the keep_alive table

In your Supabase project, run this SQL command in the SQL Editor:

```sql
-- Create the keep_alive table
CREATE TABLE keep_alive (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert a test record
INSERT INTO keep_alive (id) VALUES (1);

-- Enable public SELECT access (for the API key to work)
ALTER TABLE keep_alive ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow public read access" ON keep_alive
FOR SELECT USING (true);
```

### 2. Get your Supabase credentials

1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **API**
3. Copy your:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **anon/public key** (the `anon` key, not the `service_role` key)

## 🛠️ Local Setup

### 1. Clone and install dependencies

```bash
# Navigate to your project directory
cd tms-keep-alive

# Install dependencies (axios should already be installed)
npm install
```

### 2. Configure environment variables

Create a `.env` file in the root directory:

```bash
# Copy the example file
cp .env.example .env
```

Edit the `.env` file with your Supabase credentials:

```env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key-here
```

### 3. Test locally

Run the test script to verify your configuration:

```bash
npm test
```

You should see output like:
```
🧪 Testing Supabase keep-alive function locally...

✅ Environment variables found
📍 Supabase URL: https://your-project-id.supabase.co
🔑 API Key: eyJhbGciOiJIUzI1NiIs...

🚀 Making test request to Supabase...
✅ Success! Supabase responded:
   Status: 200
   Duration: 245ms
   Records found: 1
✅ keep_alive table has data - perfect!

🎉 All tests passed! Your configuration is ready for deployment.
```

## 🚀 Deployment to Vercel

### 1. Install Vercel CLI (if not already installed)

```bash
npm install -g vercel
```

### 2. Login to Vercel

```bash
vercel login
```

### 3. Deploy the project

From your project directory:

```bash
# First deployment (will ask configuration questions)
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? (select your account)
# - Link to existing project? No
# - Project name: tms-keep-alive (or your preferred name)
# - Directory: ./ (current directory)
# - Override settings? No
```

### 4. Add environment variables to Vercel

After deployment, add your environment variables:

```bash
# Add Supabase URL
vercel env add SUPABASE_URL

# Add Supabase API Key
vercel env add SUPABASE_KEY
```

When prompted:
- **Environment**: Choose `Production`, `Preview`, and `Development`
- **Value**: Paste your actual Supabase URL and API key

Alternatively, you can add environment variables through the Vercel dashboard:
1. Go to your project on [vercel.com](https://vercel.com)
2. Navigate to **Settings** → **Environment Variables**
3. Add `SUPABASE_URL` and `SUPABASE_KEY`

### 5. Deploy to production

```bash
vercel --prod
```

### 6. Enable cron jobs (Important!)

⚠️ **Cron jobs require a Pro plan on Vercel.** However, you can:

1. **Upgrade to Vercel Pro** ($20/month) to use the built-in cron functionality
2. **Use an external cron service** (free alternatives):
   - [cron-job.org](https://cron-job.org) (free)
   - [EasyCron](https://www.easycron.com) (free tier)
   - [Uptime Robot](https://uptimerobot.com) (free tier with monitoring)

#### Option A: Vercel Pro (Recommended)
If you upgrade to Vercel Pro, the cron job is already configured in `vercel.json` and will run automatically.

#### Option B: External Cron Service (Free)
1. Sign up for a free cron service like [cron-job.org](https://cron-job.org)
2. Create a new cron job with:
   - **URL**: `https://your-vercel-app.vercel.app/api/ping`
   - **Schedule**: `0 0 * * *` (daily at midnight UTC)
   - **Method**: GET

## 🧪 Testing the Deployment

### Test the serverless function

Visit your deployed function URL:
```
https://your-vercel-app.vercel.app/api/ping
```

You should see a JSON response like:
```json
{
  "success": true,
  "message": "Supabase keep-alive ping successful",
  "timestamp": "2024-01-15T00:00:00.000Z",
  "duration": "245ms",
  "status": 200,
  "recordsFound": 1
}
```

### Monitor the logs

Check your Vercel function logs:
1. Go to your project dashboard on [vercel.com](https://vercel.com)
2. Navigate to **Functions** → **api/ping.js**
3. View the execution logs

## 📁 Project Structure

```
tms-keep-alive/
├── api/
│   └── ping.js              # Serverless function for Supabase ping
├── node_modules/            # Dependencies
├── .env.example             # Environment variables template
├── .gitignore              # Git ignore file
├── package.json            # Project configuration
├── package-lock.json       # Dependency lock file
├── README.md               # This file
├── test-local.js           # Local testing script
└── vercel.json             # Vercel configuration with cron job
```

## 💰 Cost Breakdown (Free Tiers)

### Supabase Free Tier
- ✅ **Database**: 500MB storage
- ✅ **API requests**: 50,000 per month
- ✅ **Bandwidth**: 5GB per month
- ✅ **Our usage**: ~30 tiny requests per month (well within limits)

### Vercel Free Tier
- ✅ **Function executions**: 100GB-hours per month
- ✅ **Function duration**: Up to 10 seconds
- ✅ **Bandwidth**: 100GB per month
- ✅ **Our usage**: ~30 executions per month, <1 second each
- ❌ **Cron jobs**: Requires Pro plan ($20/month)

**Total monthly cost**: $0 (with external cron service) or $20 (with Vercel Pro)

## 🔧 Troubleshooting

### Common Issues

#### 1. "Missing environment variables" error
- Ensure `SUPABASE_URL` and `SUPABASE_KEY` are set in Vercel
- Check that the values don't have extra spaces or quotes

#### 2. "Supabase API error" (401 Unauthorized)
- Verify you're using the `anon` key, not the `service_role` key
- Check that the `keep_alive` table has public SELECT access

#### 3. "Network error" or timeouts
- Verify your Supabase project is not paused
- Check your Supabase project URL is correct

#### 4. Cron job not running
- Verify you have Vercel Pro or are using an external cron service
- Check the cron schedule syntax in `vercel.json`

### Debug Steps

1. **Test locally first**: Always run `npm test` before deploying
2. **Check Vercel logs**: Monitor function execution in the Vercel dashboard
3. **Test the endpoint**: Manually visit the `/api/ping` URL
4. **Verify Supabase**: Check your Supabase project dashboard for activity

## 🔒 Security Notes

- The `anon` key is safe to use in client-side code and serverless functions
- Row Level Security (RLS) policies protect your data
- Environment variables keep credentials secure
- The function only performs read operations

## 📝 License

MIT License - feel free to modify and use for your projects.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Happy coding!** 🚀 Your Supabase project will now stay active indefinitely on the free tier.