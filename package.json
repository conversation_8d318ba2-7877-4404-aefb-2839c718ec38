{"name": "tms-keep-alive", "version": "1.0.0", "description": "Automated Supabase keep-alive service to prevent free tier project pausing", "main": "api/ping.js", "scripts": {"test": "node test-local.js", "dev": "vercel dev", "deploy": "vercel --prod"}, "keywords": ["supabase", "keep-alive", "vercel", "serverless", "automation"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.11.0"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"dotenv": "^17.2.2"}}