const axios = require('axios');

/**
 * Vercel serverless function to keep Supabase database active
 * This function sends a GET request to the Supabase REST API to prevent
 * the free tier project from pausing after 7 days of inactivity
 */
export default async function handler(req, res) {
  // Only allow GET requests for security
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: 'Method not allowed',
      message: 'Only GET requests are supported'
    });
  }

  try {
    // Validate environment variables
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing environment variables:', {
        hasUrl: !!supabaseUrl,
        hasKey: !!supabaseKey
      });

      return res.status(500).json({
        error: 'Configuration error',
        message: 'Missing required environment variables'
      });
    }

    // Log the ping attempt
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Starting Supabase keep-alive ping...`);

    // Construct the API endpoint for the keep_alive table
    const apiUrl = `${supabaseUrl}/rest/v1/keep_alive?select=id&limit=1`;

    // Configure the request headers
    const headers = {
      'apikey': supabaseKey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal'
    };

    // Make the request to Supabase
    const startTime = Date.now();
    const response = await axios.get(apiUrl, {
      headers,
      timeout: 10000 // 10 second timeout
    });

    const duration = Date.now() - startTime;

    // Log successful response
    console.log(`[${timestamp}] Supabase ping successful:`, {
      status: response.status,
      duration: `${duration}ms`,
      dataLength: response.data?.length || 0
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Supabase keep-alive ping successful',
      timestamp,
      duration: `${duration}ms`,
      status: response.status,
      recordsFound: response.data?.length || 0
    });

  } catch (error) {
    const timestamp = new Date().toISOString();

    // Log the error details
    console.error(`[${timestamp}] Supabase ping failed:`, {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    // Determine error type and appropriate response
    if (error.code === 'ECONNABORTED') {
      return res.status(408).json({
        success: false,
        error: 'Request timeout',
        message: 'Supabase request timed out',
        timestamp
      });
    }

    if (error.response) {
      // HTTP error response from Supabase
      return res.status(error.response.status).json({
        success: false,
        error: 'Supabase API error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        timestamp
      });
    }

    if (error.request) {
      // Network error
      return res.status(503).json({
        success: false,
        error: 'Network error',
        message: 'Unable to reach Supabase',
        timestamp
      });
    }

    // Generic error
    return res.status(500).json({
      success: false,
      error: 'Internal error',
      message: error.message,
      timestamp
    });
  }
}