/**
 * Local test script for the Supabase keep-alive function
 * Run this to test your configuration before deploying to Vercel
 */

require('dotenv').config();
const axios = require('axios');

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase keep-alive function locally...\n');

  // Check environment variables
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing environment variables!');
    console.log('Please create a .env file with:');
    console.log('SUPABASE_URL=your-supabase-url');
    console.log('SUPABASE_KEY=your-supabase-key');
    process.exit(1);
  }

  console.log('✅ Environment variables found');
  console.log(`📍 Supabase URL: ${supabaseUrl}`);
  console.log(`🔑 API Key: ${supabaseKey.substring(0, 20)}...`);

  try {
    // Test the same request that the serverless function will make
    const apiUrl = `${supabaseUrl}/rest/v1/keep_alive?select=id&limit=1`;

    const headers = {
      'apikey': supabaseKey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal'
    };

    console.log('\n🚀 Making test request to Supabase...');
    const startTime = Date.now();

    const response = await axios.get(apiUrl, {
      headers,
      timeout: 10000
    });

    const duration = Date.now() - startTime;

    console.log('✅ Success! Supabase responded:');
    console.log(`   Status: ${response.status}`);
    console.log(`   Duration: ${duration}ms`);
    console.log(`   Records found: ${response.data?.length || 0}`);

    if (response.data?.length > 0) {
      console.log('✅ keep_alive table has data - perfect!');
    } else {
      console.log('⚠️  keep_alive table is empty - make sure to add at least one row');
    }

  } catch (error) {
    console.error('❌ Test failed:');

    if (error.response) {
      console.error(`   HTTP ${error.response.status}: ${error.response.statusText}`);
      console.error(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('   Network error - could not reach Supabase');
    } else {
      console.error(`   Error: ${error.message}`);
    }

    process.exit(1);
  }

  console.log('\n🎉 All tests passed! Your configuration is ready for deployment.');
}

testSupabaseConnection();